# UAT Environment Configuration
server.port=8080

# Database Configuration - UAT
spring.datasource.url=******************************************************
spring.datasource.username=postgres
spring.datasource.password=Thanhnx@123$
spring.datasource.hikari.minimumIdle=5
spring.datasource.hikari.maximumPoolSize=200
spring.datasource.hikari.idleTimeout=30000
spring.datasource.hikari.poolName=HikariCP
spring.datasource.hikari.maxLifetime=2000000
spring.datasource.hikari.connectionTimeout=30000

# JPA Configuration - UAT
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.default_schema=public
spring.jpa.properties.hibernate.globally_quoted_identifiers=true

# Flyway Configuration - UAT (disabled since using ddl-auto=update)
spring.flyway.enabled=false

# Quartz Configuration - UAT
spring.quartz.job-store-type=jdbc
spring.quartz.jdbc.initialize-schema=always
spring.quartz.properties.org.quartz.jobStore.class=org.springframework.scheduling.quartz.LocalDataSourceJobStore
spring.quartz.properties.org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
spring.quartz.properties.org.quartz.jobStore.isClustered=true
spring.quartz.properties.org.quartz.jobStore.useProperties=true
spring.quartz.properties.org.quartz.jobStore.tablePrefix=QRTZ_

# Security Configuration - UAT
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://identity.pronexus.vn/realms/pronexus_dev/protocol/openid-connect/certs

# Keycloak Configuration - UAT
keycloak.realm=pronexus_dev
keycloak.auth-server-url=https://identity.pronexus.vn
keycloak.master-username=admin
keycloak.master-password=admin
keycloak.master-client-id=admin-cli
keycloak.client-id=pronexus_dev
keycloak.client-secret=m6tCVoNKq9mvaXfxoY4EXQJu489xOxb2

# Feign Configuration - UAT
feign.user.url=https://api.pronexus.vn/user

# Logging Configuration - UAT
logging.level.org.springframework.web.client.RestTemplate=DEBUG
logging.level.org.springframework.cloud.openfeign.Feign=DEBUG