package com.salaryadvance.jobschedule.job;

import com.salaryadvance.commonlibrary.auth.ServiceAuthenticationClient;
import com.salaryadvance.jobschedule.feign.UserServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * Job để refresh ZNS access token định kỳ
 */
@Slf4j
@Component
public class ZnsTokenRefreshJob implements Job {

    @Autowired
    private UserServiceClient userServiceClient;

    @Autowired
    private ServiceAuthenticationClient serviceAuthenticationClient;

    @Override
    public void execute(JobExecutionContext context) {
        JobDataMap dataMap = context.getMergedJobDataMap();
        log.info("ZnsTokenRefreshJob is running with id: {}", dataMap.get("id"));
        
        try {
            // Lấy service token để gọi user service
            String serviceToken = serviceAuthenticationClient.getServiceToken();
            ResponseEntity<Object> response = userServiceClient.refreshZnsToken(serviceToken);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("ZNS token refresh thành công: {}", response.getBody());
            } else {
                log.error("ZNS token refresh thất bại với status: {}", response.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("Lỗi khi refresh ZNS token: {}", e.getMessage(), e);
        }
    }
}
