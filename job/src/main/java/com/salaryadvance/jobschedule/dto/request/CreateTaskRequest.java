package com.salaryadvance.jobschedule.dto.request;

import com.salaryadvance.jobschedule.enums.JobType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class CreateTaskRequest {
    @NotEmpty(message = "Name is required")
    private String name;

    @NotEmpty(message = "Group is required")
    private String group;

    @NotEmpty(message = "Cron expression is required")
    private String cronExpression;

    @NotNull(message = "Job type is required")
    private JobType jobType;
}
