package com.salaryadvance.jobschedule.dto.response;

import com.salaryadvance.jobschedule.entity.Task;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;



@Getter
@Setter
@Builder
public class TaskResponse {
    private String id;

    private String name;

    private String group;

    private String cronExpression;

    private String jobType;

    private Boolean isActive;

    private Long createdAt;

    private Long updatedAt;

    private String createdBy;

    private String updatedBy;

    /**
     * Convert to TaskResponse
     *
     * @param task Task
     * @return TaskResponse
     */
    public static TaskResponse convert(Task task) {
        return TaskResponse.builder()
            .id(task.getId().toString())
            .name(task.getName())
            .group(task.getGroup())
            .cronExpression(task.getCronExpression())
            .jobType(task.getJobType())
            .isActive(task.getIsActive())
            .createdAt(task.getCreatedAt())
            .updatedAt(task.getUpdatedAt())
            .createdBy(task.getCreatedBy())
            .updatedBy(task.getUpdatedBy())
            .build();
    }
}
