package com.salaryadvance.jobschedule.service;

import com.salaryadvance.jobschedule.dto.request.CreateTaskRequest;
import com.salaryadvance.jobschedule.dto.response.TaskResponse;
import com.salaryadvance.jobschedule.entity.Task;
import com.salaryadvance.jobschedule.enums.JobType;
import com.salaryadvance.jobschedule.repository.TaskRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class TaskService {
    private final TaskRepository taskRepository;

    private final JobService jobService;

    /**
     * Create a task (chỉ lưu vào DB, chưa đăng ký job)
     *
     * @param request CreateTaskRequest
     * @return Task
     */
    @Transactional
    public Task create(CreateTaskRequest request) {
        Task task = taskRepository.save(Task.builder()
            .name(request.getName())
            .group(request.getGroup())
            .cronExpression(request.getCronExpression())
            .jobType(request.getJobType().getValue())
            .isActive(false) // Chỉ lưu DB, chưa đăng ký job
            .build());

        log.info("Task {} created successfully (not scheduled yet)", task.getName());
        return task;
    }

    /**
     * Create and immediately schedule a task (backward compatibility)
     *
     * @param request CreateTaskRequest
     * @return Task
     */
    @Transactional
    public Task createAndSchedule(CreateTaskRequest request) {
        Task task = create(request);
        try {
            scheduleTask(task.getId());
        } catch (Exception e) {
            log.error("Error scheduling task after creation: {}", task.getName(), e);
        }
        return task;
    }

    /**
     * Schedule/Run a task (đăng ký job từ task đã có trong DB)
     *
     * @param taskId Task ID
     * @return Task đã được schedule
     */
    @Transactional
    public Task scheduleTask(UUID taskId) throws SchedulerException {
        Task task = taskRepository.findById(taskId)
            .orElseThrow(() -> new IllegalArgumentException("Task not found with id: " + taskId));

        if (task.getIsActive()) {
            log.info("Task {} is already scheduled, attempting to unschedule", task.getName());
            try {
                boolean isUnScheduled = jobService.unScheduleTaskJob(task);
                if (!isUnScheduled) {
                    log.warn("Unable to unschedule task: {}, proceeding with rescheduling", task.getName());
                }
            } catch (Exception e) {
                log.warn("Error while unscheduling task: {}, proceeding with rescheduling. Error: {}",
                    task.getName(), e.getMessage());
            }
            // Update active status regardless of unschedule result
            task.setIsActive(false);
            task = taskRepository.save(task);
        }

        // Đăng ký job dựa trên jobType
        JobType jobType = JobType.valueOf(task.getJobType());
        try {
            switch (jobType) {
                case TASK_JOB:
                    jobService.scheduleTaskJob(task);
                    break;
                case ZNS_TOKEN_REFRESH:
                    jobService.scheduleZnsTokenRefreshJob(task);
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported job type: " + task.getJobType());
            }

            // Only update active status if scheduling succeeds
            task.setIsActive(true);
            task = taskRepository.save(task);
            log.info("Task {} scheduled successfully", task.getName());
        } catch (Exception e) {
            log.error("Failed to schedule task: {}", task.getName(), e);
            throw new SchedulerException("Failed to schedule task: " + e.getMessage());
        }

        return task;
    }

    /**
     * Unschedule a task (hủy đăng ký job nhưng vẫn giữ trong DB)
     *
     * @param taskId Task ID
     * @return Task đã được unschedule
     */
    @Transactional
    public Task unscheduleTask(UUID taskId) {
        Task task = taskRepository.findById(taskId)
            .orElseThrow(() -> new IllegalArgumentException("Task not found with id: " + taskId));

        if (!task.getIsActive()) {
            log.warn("Task {} is not scheduled", task.getName());
            return task;
        }

        boolean isUnScheduled = jobService.unScheduleTaskJob(task);
        if (isUnScheduled) {
            task.setIsActive(false);
            task = taskRepository.save(task);
            log.info("Task {} unscheduled successfully", task.getName());
        } else {
            log.error("Error unscheduling task: {}", task.getName());
        }

        return task;
    }

    @Transactional
    public void delete(UUID id) {
        Task task = taskRepository.findById(id).orElseThrow(() -> new IllegalArgumentException("Task not found"));

        // Unschedule trước khi xóa
        if (task.getIsActive()) {
            boolean isUnScheduledJob = jobService.unScheduleTaskJob(task);
            if (!isUnScheduledJob) {
                log.error("Error unscheduling task before deletion: {}", task.getName());
                return;
            }
        }

        taskRepository.delete(task);
        log.info("Task {} deleted successfully", task.getName());
    }

    @Transactional
    public void delete(String id) {
        delete(UUID.fromString(id));
    }

    /**
     * Tạo ZNS token refresh task (convenience method)
     *
     * @param cronExpression Cron expression
     * @return Task đã tạo
     */
    @Transactional
    public Task createZnsTokenRefreshTask(String cronExpression) {
        CreateTaskRequest request = CreateTaskRequest.builder()
            .name("ZNS_TOKEN_REFRESH")
            .group("ZNS_GROUP")
            .cronExpression(cronExpression)
            .jobType(JobType.ZNS_TOKEN_REFRESH)
            .build();

        return create(request);
    }

    /**
     * Tạo và schedule ZNS token refresh task ngay lập tức
     *
     * @param cronExpression Cron expression
     * @return Task đã tạo và schedule
     */
    @Transactional
    public Task createAndScheduleZnsTokenRefreshTask(String cronExpression) {
        CreateTaskRequest request = CreateTaskRequest.builder()
            .name("ZNS_TOKEN_REFRESH")
            .group("ZNS_GROUP")
            .cronExpression(cronExpression)
            .jobType(JobType.ZNS_TOKEN_REFRESH)
            .build();

        return createAndSchedule(request);
    }

    /**
     * Lấy tất cả tasks với phân trang và filter
     *
     * @param name Task name filter
     * @param jobType Job type filter
     * @param isActive Active status filter
     * @param pageable Pagination info
     * @return Page of TaskResponse
     */
    public Page<TaskResponse> getAllTasks(String name, String jobType, Boolean isActive, Pageable pageable) {
        Specification<Task> spec = Specification.where(null);

        if (name != null && !name.trim().isEmpty()) {
            spec = spec.and((root, query, criteriaBuilder) ->
                criteriaBuilder.like(criteriaBuilder.lower(root.get("name")),
                    "%" + name.toLowerCase() + "%"));
        }

        if (jobType != null && !jobType.trim().isEmpty()) {
            spec = spec.and((root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("jobType"), jobType));
        }

        if (isActive != null) {
            spec = spec.and((root, query, criteriaBuilder) ->
                criteriaBuilder.equal(root.get("isActive"), isActive));
        }

        Page<Task> taskPage = taskRepository.findAll(spec, pageable);
        return taskPage.map(TaskResponse::convert);
    }

    /**
     * Lấy tất cả tasks không phân trang (backward compatibility)
     *
     * @return List of tasks
     */
    public java.util.List<Task> getAllTasksNoPaging() {
        return taskRepository.findAll();
    }

    /**
     * Lấy task theo ID
     *
     * @param taskId Task ID
     * @return Task
     */
    public Task getTaskById(UUID taskId) {
        return taskRepository.findById(taskId)
            .orElseThrow(() -> new IllegalArgumentException("Task not found with id: " + taskId));
    }
}
