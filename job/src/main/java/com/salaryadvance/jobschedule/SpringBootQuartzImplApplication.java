package com.salaryadvance.jobschedule;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@SpringBootApplication(scanBasePackages = {"com.salaryadvance.jobschedule", "com.salaryadvance.commonlibrary"})
@EnableFeignClients
@EnableJpaAuditing(auditorAwareRef = "auditorAwareImpl")
public class SpringBootQuartzImplApplication {
    public static void main(String[] args) {
        SpringApplication.run(SpringBootQuartzImplApplication.class, args);
    }
}
