package com.salaryadvance.jobschedule.entity;

import com.salaryadvance.commonlibrary.persistence.AuditableEntity;
import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.util.UUID;

@Entity
@Table(name = "qrtz_tasks", schema = "public")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@EntityListeners(AuditingEntityListener.class)
public class Task extends AuditableEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "\"group\"", nullable = false) // Escape reserved keyword
    private String group;

    @Column(name = "cron_expression")
    private String cronExpression;

    @Column(name = "job_type", nullable = false)
    private String jobType; // TASK_JOB, ZNS_TOKEN_REFRESH, etc.

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = false; // false = chỉ lưu DB, true = đã đăng ký job
}
