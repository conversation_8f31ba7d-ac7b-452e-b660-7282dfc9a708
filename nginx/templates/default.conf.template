server {
  listen 80;
  server_name api.pronexus.vn;
  # uncomment to redirect to HTTPS
  return 301 https://$host$request_uri;
}

server {
  listen 443 ssl;
  server_name api.pronexus.vn;

  ssl_certificate /etc/nginx/ssl/api/fullchain.pem;
  ssl_certificate_key /etc/nginx/ssl/api/privkey.pem;

  # Additional SSL configuration (for development purposes)
  ssl_protocols TLSv1.2 TLSv1.3;
  ssl_ciphers 'ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';

  location /file/ {
    proxy_pass http://file;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
  location /swagger-ui/ {
    proxy_pass http://swagger-ui:8080;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
  location /user/ {
    proxy_pass http://user;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
  location /webhook/ {
    set $docker_webhook_host "webhook";
    proxy_pass http://$docker_webhook_host;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
  location /integration/ {
    proxy_pass http://integration;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
  location /salary-advance/ {
    proxy_pass http://host.docker.internal:8089;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
  location /portal/ {
    proxy_pass http://portal;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
  location /job/ {
    proxy_pass http://job;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Authorization $http_authorization;
    proxy_set_header Connection "";
    proxy_http_version 1.1;
  }
}

server {
  listen 80;
  server_name admin.pronexus.vn;
  return 301 https://$host$request_uri;
}

server {
  listen 443 ssl;
  server_name admin.pronexus.vn;

  ssl_certificate /etc/nginx/ssl/admin/fullchain.pem;
  ssl_certificate_key /etc/nginx/ssl/admin/privkey.pem;

  location / {
    proxy_pass http://portal-admin-frontend;
  }
}

server {
  listen 80;
  server_name identity.pronexus.vn;
  return 301 https://$host$request_uri;
}

server {
  listen 443 ssl;
  server_name identity.pronexus.vn;

  ssl_certificate /etc/nginx/ssl/identity/fullchain.pem;
  ssl_certificate_key /etc/nginx/ssl/identity/privkey.pem;
  large_client_header_buffers 8 32k;

  add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

  location / {
    proxy_pass http://identity;  # Giữ HTTP vì Keycloak trong Docker không có HTTPS
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-Proto https;  # Đánh dấu là HTTPS để Keycloak hiểu đúng
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Frame-Options SAMEORIGIN;
    proxy_redirect off;

    add_header Content-Security-Policy "frame-src 'self' https://admin.pronexus.vn https://localhost:4200;";

    # Workaround để tránh lỗi CORS trong ReactJS
    if ($uri ~ "^(.*)/(protocol/openid-connect/auth|login/oauth2/code)(.*)") {
      add_header Access-Control-Allow-Origin *;
    }
  }

  location /resources/ {
    proxy_pass http://identity;  # Giữ HTTP vì Keycloak chạy HTTP
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-Proto https;
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Frame-Options SAMEORIGIN;
    proxy_redirect off;

    add_header Content-Security-Policy "frame-src 'self' https://admin.pronexus.vn https://localhost:4200;";
  }
}