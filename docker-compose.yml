version: '3.9'

services:
  nginx:
    image: nginx:1.27.2
    container_name: nginx
    restart: unless-stopped
    volumes:
      - ./nginx/templates:/etc/nginx/templates
      - ./nginx/configuration/custom_proxy_settings.conf:/etc/nginx/conf.d/custom_proxy_settings.conf
      - /etc/letsencrypt/live/api.pronexus.vn/fullchain.pem:/etc/nginx/ssl/api/fullchain.pem:ro
      - /etc/letsencrypt/live/api.pronexus.vn/privkey.pem:/etc/nginx/ssl/api/privkey.pem:ro
      - /etc/letsencrypt/live/admin.pronexus.vn/fullchain.pem:/etc/nginx/ssl/admin/fullchain.pem:ro
      - /etc/letsencrypt/live/admin.pronexus.vn/privkey.pem:/etc/nginx/ssl/admin/privkey.pem:ro
      - /etc/letsencrypt/live/identity.pronexus.vn/fullchain.pem:/etc/nginx/ssl/identity/fullchain.pem:ro
      - /etc/letsencrypt/live/identity.pronexus.vn/privkey.pem:/etc/nginx/ssl/identity/privkey.pem:ro
    ports:
      - '80:80'
      - '443:443' # Expose port 443 for HTTPS
    networks:
      - pvcb-ungluong-backend_my-network
  identity:
    image: quay.io/keycloak/keycloak:26.0.2
    container_name: identity
    command: 'start-dev --proxy-headers xforwarded --import-realm'
    environment:
      KC_TRANSACTION_TIMEOUT: 300 # Đặt timeout là 300 giây (5 phút)
      KC_BOOTSTRAP_ADMIN_USERNAME: admin
      KC_BOOTSTRAP_ADMIN_PASSWORD: admin
      KC_DB: postgres
      KC_DB_URL: *********************************************
      KC_DB_USERNAME: postgres
      KC_DB_PASSWORD: Thanhnx@123$
      KC_HTTP_PORT: 80
      KC_DB_POOL_INITIAL_SIZE: 5
      KC_DB_POOL_MIN_SIZE: 2
      KC_DB_POOL_MAX_SIZE: 20
      KC_DB_POOL_IDLE_TIMEOUT: 300000 # 5 phút
      KC_DB_POOL_MAX_LIFETIME: 1800000 # 30 phút
      KC_DB_POOL_CONNECTION_TIMEOUT: 30000 # 30 giây
      KC_DB_POOL_LEAK_DETECTION_THRESHOLD: 30000 # 30 giây để phát hiện connection không release
      KC_HOSTNAME: identity.pronexus.vn
      KC_HTTP_ENABLED: true
      KC_PROXY_HEADERS: xforwarded
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
    volumes:
      - ./identity/realm-export.json:/opt/keycloak/data/import/realm-export.json
      - ./identity/themes/custom/theme:/opt/keycloak/themes
    networks:
      - pvcb-ungluong-backend_my-network
  file:
    container_name: file
    restart: unless-stopped
    build: ./file
    image: registry.gitlab.com/pvcb-ungluong/pvcb-ungluong-backend/file:latest
    environment:
      - SPRING_DATASOURCE_URL=******************************************************
      - SERVER_SERVLET_CONTEXT_PATH=/file
      - PUBLIC_URL=${PUBLIC_API_URL}/file
      - SERVER_PORT
      - LOGGING_CONFIG
    volumes:
      - ./deployment/app-config:/app-config
      - ./sampledata/images:/images
    networks:
      - pvcb-ungluong-backend_my-network
  user:
    container_name: user
    build: ./user
    image: registry.gitlab.com/pvcb-ungluong/pvcb-ungluong-backend/user:latest
    labels:
      collect_logs_with_filebeat: 'true'
      decode_log_event_to_json_object: 'true'
      application_name: 'user'
    environment:
      - SPRING_DATASOURCE_URL=******************************************************
      - SERVER_SERVLET_CONTEXT_PATH=/user
      - PUBLIC_URL=${PUBLIC_API_URL}/user
      - SERVICES_LOCATION
      - SERVER_PORT
      - LOGGING_CONFIG
    volumes:
      - ./deployment/app-config:/app-config
    networks:
      - pvcb-ungluong-backend_my-network
  portal:
    container_name: portal
    build: ./portal
    restart: unless-stopped
    image: registry.gitlab.com/pvcb-ungluong/pvcb-ungluong-backend/portal:latest
    labels:
      collect_logs_with_filebeat: 'true'
      decode_log_event_to_json_object: 'true'
      application_name: 'portal'
    environment:
      - SPRING_DATASOURCE_URL=******************************************************
      - SERVER_SERVLET_CONTEXT_PATH=/portal
      - PUBLIC_URL=${PUBLIC_API_URL}/portal
      - SERVICES_PRODUCT
      - SERVER_PORT
      - LOGGING_CONFIG

    volumes:
      - ./deployment/app-config:/app-config
    networks:
      - pvcb-ungluong-backend_my-network
  salary-advance:
    container_name: salary-advance
    restart: unless-stopped
    build: ./salary-advance
    image: registry.gitlab.com/pvcb-ungluong/pvcb-ungluong-backend/salary-advance:latest
    labels:
      collect_logs_with_filebeat: 'true'
      decode_log_event_to_json_object: 'true'
      application_name: 'salary-advance'
    environment:
      - SPRING_DATASOURCE_URL=******************************************************
      - SERVER_SERVLET_CONTEXT_PATH=/salary-advance
      - PUBLIC_URL=${PUBLIC_API_URL}/salary-advance
      - SERVICES_PRODUCT
      - SERVER_PORT
      - LOGGING_CONFIG

    volumes:
      - ./deployment/app-config:/app-config
    networks:
      - pvcb-ungluong-backend_my-network
  integration:
    container_name: integration
    restart: unless-stopped
    build: ./integration
    image: registry.gitlab.com/pvcb-ungluong/pvcb-ungluong-backend/integration:latest
    labels:
      collect_logs_with_filebeat: 'true'
      decode_log_event_to_json_object: 'true'
      application_name: 'integration'
    environment:
      - SPRING_DATASOURCE_URL=******************************************************
      - SERVER_SERVLET_CONTEXT_PATH=/integration
      - PUBLIC_URL=${PUBLIC_API_URL}/integration
      - SERVICES_PRODUCT
      - SERVER_PORT
      - LOGGING_CONFIG

    volumes:
      - ./deployment/app-config:/app-config
    networks:
      - pvcb-ungluong-backend_my-network
  job:
    container_name: job
    restart: unless-stopped
    build: ./job
    image: registry.gitlab.com/pvcb-ungluong/pvcb-ungluong-backend/job:latest
    labels:
      collect_logs_with_filebeat: 'true'
      decode_log_event_to_json_object: 'true'
      application_name: 'job'
    environment:
      - SPRING_DATASOURCE_URL=******************************************************
      - SERVER_SERVLET_CONTEXT_PATH=/job
      - PUBLIC_URL=${PUBLIC_API_URL}/job
      - SERVICES_PRODUCT
      - SERVER_PORT
      - LOGGING_CONFIG
    volumes:
      - ./deployment/app-config:/app-config
    networks:
      - pvcb-ungluong-backend_my-network
  swagger-ui:
    image: swaggerapi/swagger-ui:v5.17.14
    restart: unless-stopped
    environment:
      - BASE_URL=/swagger-ui
      - URLS
      - OAUTH_CLIENT_ID=swagger-ui
      - OAUTH_USE_PKCE=true
    networks:
      - pvcb-ungluong-backend_my-network
  postgres:
    restart: unless-stopped
    container_name: postgres
    image: debezium/postgres:16-alpine
    build: ./docker/postgres
    hostname: ${POSTGRES_HOST}
    ports:
      - '${POSTGRES_PORT}:${POSTGRES_PORT}'
    volumes:
      - ./docker/postgres/postgresql.conf:/usr/share/postgresql/postgresql.conf
      - ./postgres_init.sql:/docker-entrypoint-initdb.d/postgres_init.sql
      - postgres:/var/lib/postgresql/data
    command: postgres -c 'max_connections=488'
    shm_size: 4g # Tăng bộ nhớ chia sẻ lên 4GB
    environment:
      - POSTGRES_USER
      - POSTGRES_PASSWORD
    networks:
      - pvcb-ungluong-backend_my-network
  pgadmin:
    image: dpage/pgadmin4:2024-10-19-2
    volumes:
      - pgadmin:/var/lib/pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    networks:
      - pvcb-ungluong-backend_my-network
  zookeeper:
    container_name: zookeeper
    image: debezium/zookeeper:2.7.3.Final
    restart: always
    ports:
      - 2181:2181
      - 2888:2888
      - 3888:3888
    networks:
      - pvcb-ungluong-backend_my-network
  kafka:
    container_name: kafka
    restart: unless-stopped
    image: confluentinc/cp-kafka:7.7.1
    hostname: ${KAFKA_SERVICE_HOST}
    depends_on:
      - zookeeper
    ports:
      - ${KAFKA_SERVICE_PORT}:${KAFKA_SERVICE_PORT}
      - 29092:29092
    environment:
      - KAFKA_BROKER_ID
      - KAFKA_ZOOKEEPER_CONNECT
      - KAFKA_ADVERTISED_LISTENERS
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP
      - KAFKA_INTER_BROKER_LISTENER_NAME
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
    networks:
      - pvcb-ungluong-backend_my-network
  kafka-connect:
    container_name: kafka-connect
    image: debezium/connect:2.7.3.Final
    restart: always
    ports:
      - 8083:8083
      - 5005:5005
    depends_on:
      - kafka
    environment:
      - BOOTSTRAP_SERVERS=kafka:9092
      - GROUP_ID=1
      - CONFIG_STORAGE_TOPIC=kafka_connect_configs
      - OFFSET_STORAGE_TOPIC=kafka_connect_offsets
    networks:
      - pvcb-ungluong-backend_my-network
  kafka-ui:
    container_name: kafka-ui
    image: provectuslabs/kafka-ui:latest
    environment:
      DYNAMIC_CONFIG_ENABLED: 'true'
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
    ports:
      - 8089:8080
    depends_on:
      - kafka
      - kafka-connect
    networks:
      - pvcb-ungluong-backend_my-network
  redis:
    image: redis:7.4.1-alpine
    restart: always
    ports:
      - '6379:6379'
    environment:
      - SPRING_DATA_REDIS_HOST
      - SPRING_DATA_REDIS_PORT
    volumes:
      - redis:/data
    networks:
      - pvcb-ungluong-backend_my-network
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.9.1
    container_name: filebeat
    command: filebeat -e -strict.perms=false
    volumes:
      - ./filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro # Configuration file
      - fb_data:/usr/share/filebeat/data:rw # Persistence data
      - /var/lib/docker/containers:/var/lib/docker/containers:ro # Docker logs
      - /var/run/docker.sock:/var/run/docker.sock:ro # Additional information about containers
    user: root # Allow access to log files and docker.sock
    restart: on-failure
    depends_on:
      - logstash
    networks:
      - pvcb-ungluong-backend_my-network
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.9.1
    container_name: elasticsearch
    ports:
      - '9200:9200'
      - '9300:9300'
    networks:
      - pvcb-ungluong-backend_my-network
    restart: always
    volumes:
      - es_data:/usr/share/elasticsearch/data
      - ./elasticsearch/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
    environment:
      - discovery.type=single-node
      - http.host=0.0.0.0
      - transport.host=0.0.0.0
      - xpack.security.enabled=false
      - xpack.monitoring.enabled=false
      - cluster.name=elasticsearch
      - bootstrap.memory_lock=true
  kibana:
    image: docker.elastic.co/kibana/kibana:7.9.1
    container_name: kibana
    networks:
      - pvcb-ungluong-backend_my-network
    ports:
      - '5601:5601'
    depends_on:
      - elasticsearch
    volumes:
      - ./kibana/kibana.yml:/usr/share/kibana/config/kibana.yml
      - kb_data:/usr/share/kibana/data
  logstash:
    image: docker.elastic.co/logstash/logstash:7.9.1
    container_name: logstash
    networks:
      - pvcb-ungluong-backend_my-network
    ports:
      - '5044:5044'
      - '9600:9600'
    volumes:
      - ./logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./logstash/logstash.yml:/usr/share/logstash/config/logstash.yml
      - ls_data:/usr/share/logstash/data
    depends_on:
      - elasticsearch

networks:
  pvcb-ungluong-backend_my-network:
    driver: bridge
    name: pvcb-ungluong-backend_my-network
    labels:
      com.docker.compose.network: 'pvcb-ungluong-backend_my-network'

volumes:
  postgres:
  pgadmin:
  redis:
  es_data:
  ls_data:
  kb_data:
  fb_data:
